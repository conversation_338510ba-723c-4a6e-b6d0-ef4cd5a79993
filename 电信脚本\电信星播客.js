const axios = require('axios');
const crypto = require('crypto');
const https = require('https');
const schedule = require('node-schedule');
const querystring = require('querystring');
const { URL } = require('url');
const fs = require('fs').promises;
const agent = new https.Agent({
  rejectUnauthorized: false,
  secureOptions: crypto.constants.SSL_OP_LEGACY_SERVER_CONNECT
});

class GlobalVars {
  static period = "";
  static live_id = "";
  static active_code = "";
  //static OCR_SERVER = "http://1.181.69.197:16666/ocr";//ocr服务地址
  //static OCR_SERVER = "http://111.119.250.86:8000/ocr";//ocr服务地址
  static OCR_SERVER = "http://47.111.126.11:8000/ocr";//ocr服务地址
  //static OCR_SERVER = "http://47.116.214.168:7777";
  //static OCR_SERVER = "http://111.229.140.222:777";
  //static OCR_SERVER = "http://139.159.193.117:777";
  //static OCR_SERVER = "http://120.46.18.106:7777";
  //static OCR_SERVER = "http://47.115.46.214:7777";
  //static OCR_SERVER = "http://122.152.214.128:777";
  //static OCR_SERVER = "http://120.26.83.178:7777";
  //static OCR_SERVER = "http://149.88.87.29:7777"; // 405错误，不可用
  //static OCR_SERVER = "http://111.119.250.86:8000/ocr"; // 尝试这个服务器
  //static OCR_SERVER = "http://1.92.97.94:7777";
  //static OCR_SERVER = "http://47.103.157.136:7777";
  //static OCR_SERVER = "http://27.106.103.245:7777";
  //static OCR_SERVER = "http://116.196.119.202:777";
  //static OCR_SERVER = "http://119.3.179.129:7777";
  //static OCR_SERVER = "http://47.119.166.98:7777";
  //static OCR_SERVER = "http://49.234.19.78:7777";
  //static OCR_SERVER = "http://110.40.43.178:7777";
  //static OCR_SERVER = "http://39.97.51.244:7777";
  //static OCR_SERVER = "http://175.178.73.197:7777";
  //static OCR_SERVER = "http://120.26.12.74:7777";
  //static OCR_SERVER = "http://47.98.54.181:7777";
  //static OCR_SERVER = "http://124.243.176.220:777";
  //static OCR_SERVER = "http://8.154.41.237:7777";
  //static OCR_SERVER = "http://43.139.186.245:7777";
  //static OCR_SERVER = "http://45.33.55.33:7777";
  //static OCR_SERVER = "http://121.40.232.7:9888";
  //static OCR_SERVER = "http://101.132.237.87:7777";
  //static OCR_SERVER = "http://1.12.65.163:7777";
  //static OCR_SERVER = "http://39.107.79.200:7777";
  //static OCR_SERVER = "http://106.14.181.244:7777";
  //static OCR_SERVER = "http://113.45.175.177:7777";
  //static OCR_SERVER = "http://47.122.63.70:7777";
  //static OCR_SERVER = "http://121.196.59.119:7777";
  //static OCR_SERVER = "http://47.237.111.68:7777";
  //static OCR_SERVER = "http://122.51.82.154:7777";
  //static OCR_SERVER = "http://152.69.222.114:7777";
  //static OCR_SERVER = "http://146.56.142.244:7777";
  //static OCR_SERVER = "http://47.92.173.165:7777";
  //static OCR_SERVER = "http://146.235.213.208:777";
  //static OCR_SERVER = "http://103.38.82.133:7777";
  // 商品关键词配置 - 在这里添加您要查找的商品
  static PRODUCT_KEYWORDS = [
    '大转盘',
    //'10GB 流量包'
    '70元100G流量包'
  ];
  // 调试模式：设置为true时会显示所有商品信息
  static DEBUG_MODE = process.env.DEBUG_MODE === 'true';
}
const loadTokenFile = 'chinaTelecom_xbk_cache.json';
let loadToken = {};

try {
  loadToken = require(`./${loadTokenFile}`);
} catch (error) {
  loadToken = {};
}
const key = Buffer.from('1234567`90koiuyhgtfrdews');
const iv = Buffer.alloc(8, 0);
const lotteryPublicKey = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDIPOHtjs6p4sTlpFvrx+ESsYkEvyT4JB/dcEbU6C8+yclpcmWEvwZFymqlKQq89laSH4IxUsPJHKIOiYAMzNibhED1swzecH5XLKEAJclopJqoO95o8W63Euq6K+AKMzyZt1SEqtZ0mXsN8UPnuN/5aoB3kbPLYpfEwBbhto6yrwIDAQAB
-----END PUBLIC KEY-----`;
const loginPublicKey = `-----BEGIN PUBLIC KEY-----
MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDBkLT15ThVgz6/NOl6s8GNPofdWzWbCkWnkaAm7O2LjkM1H7dMvzkiqdxU02jamGRHLX/ZNMCXHnPcW/sDhiFCBN18qFvy8g6VYb9QtroI09e176s+ZCtiv7hbin2cCTj99iUpnEloZm19lwHyo69u5UMiPMpq0/XKBO8lYhN/gwIDAQAB
-----END PUBLIC KEY-----`;
const axiosInstance = axios.create({
  httpsAgent: agent,
  timeout: 30000,
  maxRedirects: 0, 
  validateStatus: status => true, 
  headers: {
    'User-Agent': 'CtClient;10.4.1;Android;13;22081212C;NTQzNzgx!#!MTgwNTg1'
  }
});
const encrypt = (text) => {
  try {
    const cipher = crypto.createCipheriv('des-ede3-cbc', key, iv);
    let encrypted = cipher.update(text, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    return encrypted;
  } catch (error) {
    console.error('加密失败:', error);
    return text;
  }
};

const decrypt = (text) => {
  const decipher = crypto.createDecipheriv('des-ede3-cbc', key, iv);
  let decrypted = decipher.update(Buffer.from(text, 'hex'));
  decrypted = Buffer.concat([decrypted, decipher.final()]);
  return decrypted.toString();
};
const b64 = (plaintext) => {
  try {
    const publicKey = crypto.createPublicKey(loginPublicKey);
    const encrypted = crypto.publicEncrypt(
      {
        key: publicKey,
        padding: crypto.constants.RSA_PKCS1_PADDING
      },
      Buffer.from(plaintext)
    );
    return encrypted.toString('base64');
  } catch (error) {
    console.error('登录RSA加密失败:', error);
    return null;
  }
};

const rsaEncryptLong = (text, publicKey, chunkSize = 117) => {
  try {
    const cipher = crypto.createPublicKey(publicKey);
    let encrypted = Buffer.alloc(0);
    for (let i = 0; i < text.length; i += chunkSize) {
      const chunk = text.slice(i, i + chunkSize);
      const encryptedChunk = crypto.publicEncrypt(
        {
          key: cipher,
          padding: crypto.constants.RSA_PKCS1_PADDING
        },
        Buffer.from(chunk) 
      );
      encrypted = Buffer.concat([encrypted, encryptedChunk]);
    }
    return encrypted.toString('base64');
  } catch (error) {
    console.error('抽奖RSA加密失败:', error);
    return null;
  }
};
const encode_phone = (text) => {
  return text.split('').map(char => 
    String.fromCharCode(char.charCodeAt(0) + 2)
  ).join('');
};
const maskMiddleFour = (value) => {
  if (typeof value !== 'string') {
    throw new TypeError('输入类型错误，应为字符串');
  }
  if (value.length < 11) {
    throw new ValueError('输入的字符串长度不足以截取中间四位');
  }
  return value.slice(0, 3) + '####' + value.slice(-4);
};
const formatDate = (date) => {
  const pad = (num) => String(num).padStart(2, '0');
  return (
    date.getFullYear() +
    pad(date.getMonth() + 1) +
    pad(date.getDate()) +
    pad(date.getHours()) +
    pad(date.getMinutes()) +
    pad(date.getSeconds())
  );
};
const getTicket = async (phone, userId, token) => {
  if (!userId) return false;
  
  try {
    const timestamp = formatDate(new Date());
    const xmlData = `<Request><HeaderInfos><Code>getSingle</Code><Timestamp>${timestamp}</Timestamp><BroadAccount></BroadAccount><BroadToken></BroadToken><ClientType>#9.6.1#channel50#iPhone 14 Pro Max#</ClientType><ShopId>20002</ShopId><Source>110003</Source><SourcePassword>Sid98s</SourcePassword><Token>${token}</Token><UserLoginName>${phone}</UserLoginName></HeaderInfos><Content><Attach>test</Attach><FieldData><TargetId>${encrypt(userId)}</TargetId><Url>4a6862274835b451</Url></FieldData></Content></Request>`;

    const response = await axiosInstance.post(
      'https://appgologin.189.cn:9031/map/clientXML',
      xmlData,
      {
        headers: {
          'Content-Type': 'application/xml;charset=utf-8',
          'User-Agent': 'CtClient;10.4.1;Android;13;22081212C;NTQzNzgx!#!MTgwNTg1'
        }
      }
    );

    const match = response.data.match(/<Ticket>(.*?)<\/Ticket>/);
    if (!match) return false;
    
    return decrypt(match[1]);
  } catch (error) {
    console.error('获取ticket失败:', error);
    return false;
  }
};
const getXbkCaptcha = async (guid, SMART_COMMUNITY, phone) => {
  try {
    const response = await axiosInstance.get(
      `https://xbk.189.cn/xbkapi/api/auth/captcha?guid=${guid}`,
      {
        headers: {
          'Cookie': `SMART_COMMUNITY=${SMART_COMMUNITY}`,
          'User-Agent': 'CtClient;11.3.0;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ',
          'Accept': 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'sec-ch-ua-platform': '"Android"',
          'sec-ch-ua': '"Android WebView";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          'sec-ch-ua-mobile': '?1',
          'x-requested-with': 'com.ct.client',
          'sec-fetch-site': 'same-origin',
          'sec-fetch-mode': 'no-cors',
          'sec-fetch-dest': 'image',
          'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
        },
        responseType: 'arraybuffer'
      }
    );

    const imageBase64 = Buffer.from(response.data).toString('base64');
    const formData = new URLSearchParams();
    formData.append('image', imageBase64);
    formData.append('probability', 'false');
    formData.append('png_fix', 'false');

    const ocrResponse = await axiosInstance.post(GlobalVars.OCR_SERVER, 
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded'
        }
      }
    );

    if (!ocrResponse.data || !ocrResponse.data.data) {
      console.log(`${phone}: OCR识别失败:`, ocrResponse.data);
      return null;
    }

    const result = ocrResponse.data.data;
    console.log("server:", result);
    console.log("验证码识别结果:", result);

    try {
      let answer;
      if (result.includes('+')) {
        const nums = result.split('+');
        const num1 = parseInt(nums[0].trim());
        const num2 = parseInt(nums[1].split('=')[0].trim());
        answer = num1 + num2;
      } else if (result.includes('#')) {
        const nums = result.split('#');
        if (nums.length >= 2 && nums[1].trim()) {
          const num1 = parseInt(nums[0].trim());
          const num2 = parseInt(nums[1].trim());
          answer = num1 + num2;
        } else {
          console.log(`${phone}: 验证码格式不正确: ${result}`);
          return null;
        }
      } else {
        console.log(`${phone}: 未识别的验证码格式: ${result}`);
        return null;
      }
      return String(answer);
    } catch (error) {
      console.log(`${phone}:计算验证码结果时出错: ${error}`);
      return null;
    }
  } catch (error) {
    console.error('获取验证码失败:', error);
    return null;
  }
};
const getXbkEncryptParam = async (ticket) => {
  try {
    const response = await axiosInstance.get(
      `https://xbk.189.cn/luntanSafe/external/redirect.do?ticket=${ticket}&indexUri=lifeChannel`,
      {
        maxRedirects: 0,
        validateStatus: status => true,
        headers: {
          'User-Agent': 'CtClient;10.0.1;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ'
        }
      }
    );

    if ([301, 302, 303, 307, 308].includes(response.status)) {
      const cookies = response.headers['set-cookie'];
      if (cookies) {
        for (const cookie of cookies) {
          if (cookie.startsWith('SMART_COMMUNITY=')) {
            const value = cookie.split(';')[0].split('=')[1];
            if (value) {
              return value;
            }
          }
        }
      }
    } else {
      console.log("未触发重定向");
    }
    return null;
  } catch (error) {
    console.error('获取加密参数失败:', error);
    return null;
  }
};

const getXbkLotteryDo = async (authorization, SMART_COMMUNITY, period, liveId, active_code, phone) => {
  try {
    const guid = crypto.randomUUID();
    const captcha = await getXbkCaptcha(guid, SMART_COMMUNITY, phone);
    if (!captcha) {
      console.log(`${maskMiddleFour(phone)} 获取验证码失败`);
      return null;
    }
    const data = {
      "active_code": active_code,
      "captcha": captcha,
      "guid": guid,
      "liveId": liveId,
      "period": period
    };
    const response = await axiosInstance.post(
      'https://xbk.189.cn/xbkapi/active/v2/lottery/do',
      data,
      {
        headers: {
          'User-Agent': 'CtClient;11.3.0;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Encoding': 'gzip, deflate, br',
          'Content-Type': 'application/json;charset=UTF-8',
          'authorization': `Bearer ${authorization}`,
          'request-starttime': Date.now().toString(),
          'sec-ch-ua': '"Android WebView";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
          'sec-ch-ua-mobile': '?1',
          'sec-ch-ua-platform': '"Android"',
          'x-requested-with': 'com.ct.client',
          'origin': 'https://xbk.189.cn',
          'sec-fetch-site': 'same-origin',
          'sec-fetch-mode': 'cors',
          'sec-fetch-dest': 'empty',
          'referer': `https://xbk.189.cn/xbk/luckyDraw?version=11.3.0&active_code=${active_code}&liveId=${liveId}&period=${period}`,
          'Cookie': `SMART_COMMUNITY=${SMART_COMMUNITY}`  // 用SMART_COMMUNITY试试
        }
      }
    );

    if (response.status === 200) {
      const result = response.data;
      console.log(`${maskMiddleFour(phone)}抽奖结果:`, [result, result.msg]);
      return result;
    }
    return null;
  } catch (error) {
    console.error('抽奖失败:', error);
    return null;
  }
};
const getXbkLoginInfo = async (SMART_COMMUNITY) => {
  try {
    const response = await axiosInstance.get(
      'https://xbk.189.cn/luntanSafe/user/getLoginInfo',
      {
        headers: {
          'User-Agent': 'CtClient;10.0.1;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ',
          'Cookie': `SMART_COMMUNITY=${SMART_COMMUNITY}`
        }
      }
    );
    if (response.status === 200) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('获取登录信息失败:', error);
    return null;
  }
};
const getReqTicket = async (userId) => {
  try {
    const response = await axiosInstance.get(
      'https://xbk.189.cn/xbkapi/luntan/tool/getTicket',
      {
        params: { t: '' },
        headers: {
          'User-Agent': 'CtClient;10.0.1;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ',
          'authorization': `Bearer ${userId}`
        }
      }
    );
    if (response.status === 200) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('获取请求ticket失败:', error);
    return null;
  }
};
const getUserCode = async (ticket, period, liveId, active_code) => {
  try {
    const response = await axiosInstance.get(
      'https://xbk.189.cn/xbkapi/api/auth/dxzt',
      {
        params: {
          userID: ticket,
          type: 'luckyDraw',
          active_code: active_code,
          liveId: liveId,
          period: period
        },
        headers: {
          'User-Agent': 'CtClient;10.0.1;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ',
          'x-requested-with': 'com.ct.client'
        },
        maxRedirects: 0,
        validateStatus: status => true
      }
    );

    if ([301, 302, 303, 307, 308].includes(response.status)) {
      const location = response.headers.location;
      if (location) {
        const url = new URL(location);
        return url.searchParams.get('usercode');
      }
    }
    return null;
  } catch (error) {
    console.error('获取用户code失败:', error);
    return null;
  }
};
const getCodeToken = async (usercode, SMART_COMMUNITY) => {
  try {
    const response = await axiosInstance.post(
      'https://xbk.189.cn/xbkapi/api/auth/userinfo/codeToken',
      {
        usercode: usercode
      },
      {
        headers: {
          'User-Agent': 'CtClient;11.3.0;Android;12;Redmi K30 Pro;MDAyNDUy!#!MTgwMjQ',
          'Cookie': `SMART_COMMUNITY=${SMART_COMMUNITY}`,
          'Content-Type': 'application/json',
          'Accept': 'application/json, text/plain, */*',
          'Accept-Encoding': 'gzip, deflate, br, zstd',
          'x-requested-with': 'com.ct.client',
          'sec-fetch-site': 'same-origin',
          'sec-fetch-mode': 'cors',
          'sec-fetch-dest': 'empty'
        }
      }
    );

    if (response.status === 200) {
      return response.data;
    }
    return null;
  } catch (error) {
    console.error('获取code token失败:', error);
    return null;
  }
};
// 方案二：优化统计模式 - 找到第一个匹配商品后立即停止并兑换
const getAllLiveRoomsProducts = async () => {
  try {
    const response = await axiosInstance.get('https://xbk.189.cn/xbkapi/luntan/floor/feedFloor', {
      params: {
        page: '1',
        size: '100',
        liveType: '2',
        province: '600101'
      }
    });

    const floors = response.data.data.floors;
    console.log(`发现有${floors.length}个直播间，开始查找匹配商品（找到即停止模式）...`);

    const concurrencyLimit = 8; // 提高并发数
    let foundProduct = null;
    let processedCount = 0;

    // 使用 for...of 循环以便能够提前退出
    for (let i = 0; i < floors.length && !foundProduct; i += concurrencyLimit) {
      const batch = floors.slice(i, i + concurrencyLimit);
      const batchPromises = batch.map(async (floor) => {
        const { liveId, period, nickname } = floor;

        try {
          const products = await getAllProductsFromLiveRoom(period, liveId, nickname);
          processedCount++;
          process.stdout.write(`\r进度: ${processedCount}/${floors.length} 直播间已处理`);
          return products;
        } catch (error) {
          console.error(`\n获取${nickname}直播间商品失败:`, error);
          processedCount++;
          process.stdout.write(`\r进度: ${processedCount}/${floors.length} 直播间已处理`);
          return [];
        }
      });

      const batchResults = await Promise.all(batchPromises);

      // 检查是否找到匹配的商品
      for (const products of batchResults) {
        if (products.length > 0) {
          foundProduct = products[0]; // 取第一个匹配的商品
          console.log(`\n\n=== 找到匹配商品，立即停止搜索 ===`);
          console.log(`商品信息: ${foundProduct.nickname} - ${foundProduct.title}`);
          console.log(`活动链接: ${foundProduct.link}`);
          console.log(`period: ${foundProduct.period}, liveId: ${foundProduct.liveId}, active_code: ${foundProduct.active_code}`);

          // 设置全局变量
          GlobalVars.period = foundProduct.period;
          GlobalVars.live_id = foundProduct.liveId;
          GlobalVars.active_code = foundProduct.active_code;

          return true; // 立即返回，不再继续搜索
        }
      }

      // 批次间稍作延迟
      if (i + concurrencyLimit < floors.length && !foundProduct) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    if (!foundProduct) {
      console.log('\n未找到任何匹配的活动');
      console.log('提示：请检查关键词是否正确，或者当前没有相关活动');
      return false;
    }

    return true;

  } catch (error) {
    console.error('获取直播间信息失败:', error);
    return false;
  }
};

// 获取单个直播间的商品（找到第一个匹配商品后立即返回）
const getAllProductsFromLiveRoom = async (period, liveId, nickname) => {
  let page = 1;
  let hasMorePages = true;

  while (hasMorePages) {
    try {
      const payload = {
        headerInfos: {
          code: "getDirectSeedingGoodsList",
          timestamp: "",
          broadAccount: "",
          broadToken: "None",
          clientType: "#11.3.0#channel35#Xiaomi Redmi K30 Pro#",
          shopId: "20002",
          source: "110003",
          sourcePassword: "Sid98s",
          token: "",
          userLoginName: ""
        },
        content: {
          attach: "test",
          fieldData: {
            period: String(period),
            tabType: "XBKALL",
            page: String(page),
            liveId: String(liveId),
            account: ""
          }
        }
      };

      const response = await axiosInstance.post(
        'https://appkefu.189.cn:8301/query/getDirectSeedingGoodsList',
        payload
      );

      if (!response || !response.data || !response.data.responseData || !response.data.responseData.data) {
        break;
      }

      const goodsList = response.data.responseData.data.goodsList || [];
      const count = parseInt(response.data.responseData.data.count) || 0;
      const totalPages = count > 0 ? Math.ceil(count / 6) : 1;

      // 检查当前页的商品
      for (const goods of goodsList) {
        // 调试模式：显示所有商品信息
        if (GlobalVars.DEBUG_MODE) {
          console.log(`[调试] ${nickname} - 商品: ${goods.title}, 副标题: ${goods.subTitle}, 标签: ${JSON.stringify(goods.tags)}`);
        }

        if (
          GlobalVars.PRODUCT_KEYWORDS.some(keyword => goods.title?.includes(keyword)) ||
          goods.subTitle?.includes('点我') ||
          (goods.tags || []).includes('转盘')
        ) {
          console.log(`[匹配] ${nickname} - 找到匹配商品: ${goods.title}，立即返回`);
          if (goods.link) {
            const url = new URL(goods.link);
            // 找到第一个匹配商品后立即返回
            return [{
              nickname,
              title: goods.title,
              link: goods.link,
              period: url.searchParams.get('period'),
              liveId: url.searchParams.get('liveId'),
              active_code: url.searchParams.get('active_code')
            }];
          }
        }
      }

      // 检查是否还有更多页面
      if (page >= totalPages || goodsList.length === 0) {
        hasMorePages = false;
      } else {
        page++;
        // 减少页面间延迟
        await new Promise(resolve => setTimeout(resolve, 200));
      }

    } catch (error) {
      console.error(`获取${nickname}直播间第${page}页商品失败:`, error);
      break;
    }
  }

  return []; // 未找到匹配商品
};

// 方案一：快速查找模式（找到第一个就停止）
const getXbkInfoFast = async () => {
  try {
    const response = await axiosInstance.get('https://xbk.189.cn/xbkapi/luntan/floor/feedFloor', {
      params: {
        page: '1',
        size: '100',
        liveType: '2',
        province: '600101'
      }
    });

    const floors = response.data.data.floors;
    console.log(`发现有${floors.length}个直播间,并发查看找转盘（快速模式）`);

    // 并发查找所有直播间，限制并发数量避免过载
    const concurrencyLimit = 5; // 同时查询5个直播间

    for (let i = 0; i < floors.length; i += concurrencyLimit) {
      const batch = floors.slice(i, i + concurrencyLimit);
      const batchPromises = batch.map(async (floor) => {
        const { liveId, period, nickname } = floor;
        console.log(`开始查看${nickname}直播间`);

        try {
          const result = await getDirectSeedingGoodsList(period, liveId);
          return { nickname, liveId, period, result };
        } catch (error) {
          console.error(`查看${nickname}直播间失败:`, error);
          return { nickname, liveId, period, result: "错误" };
        }
      });

      const batchResults = await Promise.all(batchPromises);

      // 检查是否找到大转盘
      const foundTurntable = batchResults.find(r => r.result === "大转盘");
      if (foundTurntable) {
        console.log(`在${foundTurntable.nickname}直播间找到大转盘！`);
        return true;
      }

      // 批次间稍作延迟，避免请求过于频繁
      if (i + concurrencyLimit < floors.length) {
        await new Promise(resolve => setTimeout(resolve, 200));
      }
    }

    console.log('所有直播间查找完毕，未找到大转盘');
    return false;
  } catch (error) {
    console.error('获取星播客信息失败:', error);
    return false;
  }
};

// 主函数：根据环境变量选择查找模式
const getXbkInfo = async () => {
  const searchMode = process.env.SEARCH_MODE || 'STATISTICS'; // 默认使用统计模式

  console.log(`使用查找模式: ${searchMode === 'STATISTICS' ? '优化统计模式（找到第一个匹配商品立即停止并兑换）' : '快速模式（找到第一个就停止）'}`);

  if (searchMode === 'FAST') {
    return await getXbkInfoFast();
  } else {
    return await getAllLiveRoomsProducts();
  }
};
const progressBar = (current, total, barLength = 30) => {
  // 防止total为0或无效值导致的Infinity错误
  if (!total || total <= 0) {
    return `[无效进度] ${current}/未知`;
  }
  const progress = Math.round((current / total) * barLength);
  const bar = '█'.repeat(progress) + '░'.repeat(barLength - progress);
  return `[${bar}] ${current}/${total}`;
};
// 优化版本：减少延迟，提高查询效率
const getDirectSeedingGoodsList = async (period, liveId, page = 1) => {
  try {
    const payload = {
      headerInfos: {
        code: "getDirectSeedingGoodsList",
        timestamp: "",
        broadAccount: "",
        broadToken: "None",
        clientType: "#11.3.0#channel35#Xiaomi Redmi K30 Pro#",
        shopId: "20002",
        source: "110003",
        sourcePassword: "Sid98s",
        token: "",
        userLoginName: ""
      },
      content: {
        attach: "test",
        fieldData: {
          period: String(period),
          tabType: "XBKALL",
          page: String(page),
          liveId: String(liveId),
          account: ""
        }
      }
    };

    const response = await axiosInstance.post(
      'https://appkefu.189.cn:8301/query/getDirectSeedingGoodsList',
      payload
    );

    // 添加对response.data的有效性检查
    if (!response || !response.data) {
      console.error('响应数据无效');
      return "错误";
    }

    const { responseData } = response.data;

    // 添加对responseData的有效性检查
    if (!responseData || !responseData.data) {
      console.error('responseData无效或缺少data字段');
      return "错误";
    }

    const goodsList = responseData.data.goodsList || [];

    // 添加对count的有效性检查
    let count = 0;
    try {
      count = parseInt(responseData.data.count);
      // 确保count是有效数字
      if (isNaN(count) || !isFinite(count)) {
        count = goodsList.length || 0;
      }
    } catch (error) {
      count = goodsList.length || 0;
    }

    const totalPages = count > 0 ? Math.ceil(count / 6) : 1;
    // console.log(responseData.data);
    process.stdout.write(`\r检查商品列表: ${progressBar(page, totalPages)}`);

    for (const goods of goodsList) {
      if (
        GlobalVars.PRODUCT_KEYWORDS.some(keyword => goods.title?.includes(keyword)) ||
        goods.subTitle?.includes('点我') ||
        (goods.tags || []).includes('转盘')
      ) {
        console.log(`\n找到大转盘活动: ${goods.title}`);
        if (goods.link) {
          const url = new URL(goods.link);
          GlobalVars.period = url.searchParams.get('period');
          GlobalVars.live_id = url.searchParams.get('liveId');
          GlobalVars.active_code = url.searchParams.get('active_code');
          return "大转盘";
        }
      }
    }

    if (goodsList.length === 0) {
      return "没有大转盘";
    }

    if (page < totalPages) {
      // 优化：减少延迟从1000ms到300ms，提高查询速度
      await new Promise(resolve => setTimeout(resolve, 300));
      return await getDirectSeedingGoodsList(period, liveId, page + 1);
    }
    console.log('\n');
    return "没有大转盘";
  } catch (error) {
    console.error('\n获取商品列表失败:', error);
    return "错误";
  }
};

const getXbkTask = async (phones) => {
  // 移除重复查找逻辑，直接使用已找到的大转盘信息
  // 注意：active_code 可能为 null，这是正常的
  if (!GlobalVars.period || !GlobalVars.live_id) {
    console.log('错误：未找到大转盘活动信息，请先调用查找函数');
    return;
  }

  console.log("开始调用抽奖任务");
  console.log(`使用大转盘信息: period=${GlobalVars.period}, liveId=${GlobalVars.live_id}, active_code=${GlobalVars.active_code || 'null'}`);

  const phoneList = phones.split('&');
  const totalTasks = phoneList.length;
  console.log(`总任务数：${totalTasks}`);
  const promises = phoneList.map(async phoneData => {
    try {
      await processSinglePhone(phoneData);
    } catch (error) {
      console.error(`处理手机号失败:`, error);
    }
  });

  await Promise.all(promises);
  console.log("所有任务都已执行完毕!");
};
const processSinglePhone = async (phoneData) => {
  try {
    const [phone, password] = phoneData.split('@');
    let ticket = false;

    if (loadToken[phone]) {
      console.log(`${maskMiddleFour(phone)} 使用缓存登录`);
      if (loadToken[phone].userId && loadToken[phone].token) {
        ticket = await getTicket(phone, loadToken[phone].userId, loadToken[phone].token);
      }
    }

    if (!ticket) {
      console.log(`${maskMiddleFour(phone)} 使用密码登录`);
      ticket = await userLoginNormal(phone, password);
    }

    if (ticket) {
      await xbkDrawTask(phone, ticket);
      await new Promise(resolve => setTimeout(resolve, 500));
    } else {
      console.log(`${maskMiddleFour(phone)} 登录失败`);
    }
  } catch (error) {
    console.error(`处理手机号 ${maskMiddleFour(phoneData)} 时发生错误:`, error);
  }
};
const xbkDrawTask = async (phone, ticket, retryCount = 0, maxRetries = 3) => {
  if (retryCount >= maxRetries) {
    console.log(`${maskMiddleFour(phone)}达到最大重试次数`);
    return;
  }

  try {
    const SMART_COMMUNITY = await getXbkEncryptParam(ticket);
    if (!SMART_COMMUNITY) {
      console.log(`${maskMiddleFour(phone)} 获取SMART_COMMUNITY失败`);
      return;
    }

    const loginInfo = await getXbkLoginInfo(SMART_COMMUNITY);
    if (!loginInfo) {
      console.log(`${maskMiddleFour(phone)} 获取登录信息失败`);
      return;
    }

    const userId = loginInfo?.data?.sign;
    if (!userId) {
      console.log(`${maskMiddleFour(phone)} 获取userId失败`);
      return;
    }

    const reqTicket = await getReqTicket(userId);
    if (!reqTicket) {
      console.log(`${maskMiddleFour(phone)} 获取请求ticket失败`);
      return;
    }

    const finalTicket = reqTicket?.data?.ticket;
    if (!finalTicket) {
      console.log(`${maskMiddleFour(phone)} 获取finalTicket失败`);
      return;
    }

    const userCode = await getUserCode(
      finalTicket, 
      GlobalVars.period, 
      GlobalVars.live_id, 
      GlobalVars.active_code
    );
    
    if (!userCode) {
      console.log(`${maskMiddleFour(phone)} 获取用户code失败`);
      return;
    }

    const codeToken = await getCodeToken(userCode, SMART_COMMUNITY);
    if (!codeToken || !codeToken.data || !codeToken.data.token) {
      console.log(`${maskMiddleFour(phone)} 获取token失败`);
      return;
    }

    const token = codeToken.data.token;
    let encryptedValue;
    try {
      encryptedValue = rsaEncryptLong(token, lotteryPublicKey);
      
      if (!encryptedValue) {
        throw new Error('加密结果为空');
      }
    } catch (error) {
      console.error(`${maskMiddleFour(phone)} token加密失败:`, error);
      return;
    }

    console.log(`${maskMiddleFour(phone)} 准备执行抽奖...`);
    const lotteryResult = await getXbkLotteryDo(
      encryptedValue,
      SMART_COMMUNITY,
      GlobalVars.period,
      GlobalVars.live_id,
      GlobalVars.active_code,
      phone
    );

    if (lotteryResult) {
      const { msg, code } = lotteryResult;
      if (code === -2002 || msg?.includes('校验未通过')) {
        console.log(`${maskMiddleFour(phone)} 验证错误,重试第${retryCount + 1}次`);
        await new Promise(resolve => setTimeout(resolve, 2000));
        await xbkDrawTask(phone, ticket, retryCount + 1);
      }
    } else {
      console.log(`${maskMiddleFour(phone)} 抽奖失败`);
    }
  } catch (error) {
    console.error(`${maskMiddleFour(phone)} 抽奖任务执行失败:`, error);
    await new Promise(resolve => setTimeout(resolve, 2000));
    await xbkDrawTask(phone, ticket, retryCount + 1);
  }
};
const userLoginNormal = async (phone, password, maxRetries = 3) => {
  await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 1000));
  
  for (let attempt = 0; attempt < maxRetries; attempt++) {
    try {
      const alphabet = 'abcdef0123456789';
      const uuid = [
        Array(8).fill().map(() => alphabet[Math.floor(Math.random() * alphabet.length)]).join(''),
        Array(4).fill().map(() => alphabet[Math.floor(Math.random() * alphabet.length)]).join(''),
        '4' + Array(3).fill().map(() => alphabet[Math.floor(Math.random() * alphabet.length)]).join(''),
        Array(4).fill().map(() => alphabet[Math.floor(Math.random() * alphabet.length)]).join(''),
        Array(12).fill().map(() => alphabet[Math.floor(Math.random() * alphabet.length)]).join('')
      ];
      
      const timestamp = formatDate(new Date());
      const loginAuthCipherAsymmertric = `iPhone 14 15.4.${uuid[0]}${uuid[1]}${phone}${timestamp}${password.slice(0, 6)}0$$$0.`;

      const response = await axiosInstance.post(
        'https://appgologin.189.cn:9031/login/client/userLoginNormal',
        {
          headerInfos: {
            code: "userLoginNormal",
            timestamp: timestamp,
            broadAccount: "",
            broadToken: "",
            clientType: "#11.3.0#channel35#Xiaomi Redmi K30 Pro#",
            shopId: "20002",
            source: "110003",
            sourcePassword: "Sid98s",
            token: "",
            userLoginName: encode_phone(phone)
          },
          content: {
            attach: "test",
            fieldData: {
              loginType: "4",
              accountType: "",
              loginAuthCipherAsymmertric: b64(loginAuthCipherAsymmertric),
              deviceUid: uuid[0] + uuid[1] + uuid[2],
              phoneNum: encode_phone(phone),
              isChinatelecom: "0",
              systemVersion: "15.4.0",
              authentication: encode_phone(password)
            }
          }
        },
        {
          headers: {
            'Content-Type': 'application/json',
            'Accept': 'application/json',
            'User-Agent': 'CtClient;9.6.1;iOS;15.4.0;iPhone 14 Pro Max'
          }
        }
      );

      try {
        const loginResult = response.data?.responseData?.data?.loginSuccessResult;
        if (loginResult) {
          loadToken[phone] = loginResult;
          await fs.writeFile(loadTokenFile, JSON.stringify(loadToken));
          const ticket = await getTicket(phone, loginResult.userId, loginResult.token);
          if (ticket) {
            return ticket;
          }
        }
      } catch (error) {
        console.error(`${phone} userLoginNormal对象为空或格式不正确，无法获取成功`);
        loginResult = null;
      }

      console.log(`${phone} 第${attempt + 1}次登录失败:`, response.data);
      if (attempt < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 2000));
      }

    } catch (error) {
      console.error(`${phone} 第${attempt + 1}次登录发生异常:`, error);
      if (attempt < maxRetries - 1) {
        await new Promise(resolve => setTimeout(resolve, Math.random() * 1000 + 2000));
      }
    }
  }
  return false;
};
const getTimeString = () => {
  return `[${new Date().toLocaleTimeString()}]`;
};
const init = async () => {
  try {
    const result = await getXbkInfo();
    if (result) {
      return true;
    }
    return false;
  } catch (error) {
    console.error('初始化失败:', error);
    return false;
  }
};
const scheduleJobs = async () => {
  let checkCount = 0;

  const checkTurntable = async () => {
    checkCount++;
    console.clear();
    console.log(`${getTimeString()} 第${checkCount}次检查大转盘`);
    const result = await getXbkInfo();
    if (result) {
      console.log(`${getTimeString()} 找到大转盘，开始执行任务...`);
      await getXbkTask();
      // 重置全局变量，以便下次能找到新的大转盘
      GlobalVars.period = "";
      GlobalVars.live_id = "";
      GlobalVars.active_code = "";
      console.log(`${getTimeString()} 抽奖任务完成，继续查找其他直播间的大转盘...`);
    } else {
      console.log(`${getTimeString()} 未找到大转盘，1分钟后再次检查...`);
    }
    return result;
  };
  
  // 立即执行一次检查
  await checkTurntable();
  
  // 设置定时任务，每分钟检查一次
  schedule.scheduleJob('checkJob', '*/1 * * * *', async () => {
    try {
      await checkTurntable();
    } catch (error) {
      console.error(`${getTimeString()} 定时任务执行失败:`, error);
    }
  });
  
  // 保持程序运行
  console.log(`${getTimeString()} 程序将持续运行并检查大转盘...`);
  // 这里不再等待foundTurntable为true，而是让程序一直运行
  while (true) {
    await new Promise(resolve => setTimeout(resolve, 60000));
  }
};
const main = async () => {
  const phones = process.env.PHONES1 || '';
 
  // 设置多个结束时间点：上午9点、下午1点和晚上9点
  const endTimes = [
    { hours: 9, minutes: 31, seconds: 0 },
    { hours: 13, minutes: 30, seconds: 0 },
    { hours: 20, minutes: 30, seconds: 0 }
  ];

  console.log(`${getTimeString()} 程序启动`);
  
  // 为每个结束时间点设置定时器
  endTimes.forEach(timePoint => {
    const endTime = new Date();
    endTime.setHours(timePoint.hours, timePoint.minutes, timePoint.seconds);
    
    // 如果当前时间已经超过了结束时间，就不设置定时器
    if (endTime.getTime() <= Date.now()) return;
    
    console.log(`${getTimeString()} 程序将在 ${endTime.toLocaleTimeString()} 自动结束`);
    const timeUntilEnd = endTime.getTime() - Date.now();
    
    setTimeout(() => {
      console.log(`${getTimeString()} 已达到预设结束时间 ${endTime.toLocaleTimeString()}，程序自动退出`);
      process.exit(0);
    }, timeUntilEnd);
  });
 
  // 持续查找大转盘并执行任务的函数
  const continuousCheck = async () => {
    while (true) {
      // 重置全局变量，确保每次都能查找新的大转盘
      GlobalVars.period = "";
      GlobalVars.live_id = "";
      GlobalVars.active_code = "";
      
      const result = await getXbkInfo();
      if (result) {
        console.log(`${getTimeString()} 找到大转盘，开始执行任务...`);
        await getXbkTask(phones);
        console.log(`${getTimeString()} 抽奖任务完成，继续查找其他直播间的大转盘...`);
      } else {
        console.log(`${getTimeString()} 未找到大转盘，1分钟后再次检查...`);
      }
      
      // 等待1分钟后再次检查
      await new Promise(resolve => setTimeout(resolve, 60000));
    }
  };
  
  // 开始持续查找大转盘
  await continuousCheck();
};
main().catch(console.error);

module.exports = {
  main,
  getXbkInfo,
  getXbkTask,
  userLoginNormal
};