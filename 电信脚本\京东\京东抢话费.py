import aiohttp
import asyncio
import datetime
import time
import ssl
import requests


def send(message, to_user=""):
    # 强制转换 message 为字符串
    message = str(message)
    
    # 参数校验
    if not message.strip():
        raise ValueError("消息内容不能为空")
    if not to_user.startswith("wxid_"):
        print("警告:接收用户ID格式可能无效")

    url = ""
    #params = {"key": ""}
    headers = {
        "accept": "application/json",
        "Content-Type": "application/json",
    }
    data = {
        "MsgItem": [
            {
            "AtWxIDList": [
                "string"
            ],
            "ImageContent": "",
            "MsgType": 0,
            "TextContent": message,
            "ToUserName": to_user
            }
        ]
    }
    #print(data)
    try:
        response = requests.post(url,headers=headers, json=data, timeout=5)
        print("状态码:", response.status_code)
        print("响应内容:", response.json())
        return response
    except requests.exceptions.RequestException as e:
        print(f"请求失败: {e}")
        return None








# 创建不验证证书的SSL上下文
ssl_context = ssl.create_default_context()
ssl_context.check_hostname = False
ssl_context.verify_mode = ssl.CERT_NONE

#url = "https://api.m.jd.com/client.action?functionId=newBabelAwardCollection"

url = "https://api.m.jd.com/client.action?functionId=newBabelAwardCollection"

payload = {
  'body': "{\"tipsBusinessId\":\"0\",\"activityId\":\"3aEzDU3fpqYYtnNTFPAkyY3tRY8Y\",\"plusExpoTimes\":[],\"plusClickTimes\":[],\"plusCloseTimes\":[],\"channel\":\"2\",\"mitemAddrId\":\"\",\"geo\":{\"lng\":\"\",\"lat\":\"\"},\"addressId\":\"\",\"posLng\":\"\",\"posLat\":\"\",\"un_area\":\"\",\"jdv\":\"weixin|t_1000072672_17053_001|weixin|\",\"focus\":\"\",\"innerAnchor\":\"\",\"cv\":\"2.0\",\"gLng1\":\"\",\"gLat1\":\"\",\"head_area\":\"\",\"receiverLng\":\"\",\"receiverLat\":\"\",\"fullUrl\":\"https://pro.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html?PTAG=17053.1.1&utm_source=weixin&utm_medium=weixin&utm_campaign=t_1000072672_17053_001&preventPV=1&forceCurrentView=1\"}",
  #'body': "{\"activityId\":\"3aEzDU3fpqYYtnNTFPAkyY3tRY8Y\",\"gridInfo\":\"\",\"transParam\":\"{\\\"bsessionId\\\":\\\"e401fb54-5a7a-44d9-9d4d-fee6cdcabdca\\\",\\\"babelChannel\\\":\\\"\\\",\\\"actId\\\":\\\"01654339\\\",\\\"enActId\\\":\\\"3aEzDU3fpqYYtnNTFPAkyY3tRY8Y\\\",\\\"pageId\\\":\\\"5577542\\\",\\\"encryptCouponFlag\\\":\\\"1\\\",\\\"requestChannel\\\":\\\"h5\\\",\\\"jdAtHomePage\\\":\\\"0\\\",\\\"utmFlag\\\":\\\"0\\\",\\\"locType\\\":\\\"1\\\"}\",\"scene\":\"1\",\"args\":\"key=D13B7F6D8192FF5BE243F5A3E07A45192B989C18A5F5BDD00F4B07608E0C8522C563EA8CFE983A8FF87F59DF95B22044_bingo,roleId=6D9DD4D7C626122B36F1784470F60C64678C1BF27E062FFFCED363DBD3ED0F2D06DC287BB6993CD7804BA46B9D64EC316140F003AE56D6EAF29D0A2EF1AC05E3AF9ECF8EB575CF9EB4009B7E9C3DD8863CBADC6B7CDFDDCD8CD921535F507D1E47F5F80F994ECD38AC4369F4BE147B64CB8CF0D4AADF2D45C810D7EC440AF3222E142107B515DB14AF391ED9589DCC99DF5239ABE17726C4F0063303FB6BE0D8_bingo,strengthenKey=19F512DCD8EE34ABE9C4FB4A92C2F42A4947DEFA907BF94DFB8DF030CBBA5691_bingo\",\"platform\":\"1\",\"orgType\":\"2\",\"openId\":\"-1\",\"pageClickKey\":\"-1\",\"eid\":\"QNOPZQ6KTBCMMYUWXDL7AWW5IOQU66SBZFOSLZAQ4N36HWUDSYJP4CVZVJ6PQHEBD5B2JEPVENAVIXM2MIZ7LD5VVY\",\"fp\":\"a221c9f961b0fbf03bc014cdb9c97d98\",\"shshshfp\":\"cd46b54c5aad5987bd2e5de6e79ac3c9\",\"shshshfpa\":\"a1f6725a-4466-1022-b7c0-cdc0d612c3be-1748935574\",\"shshshfpb\":\"BApXSj3aWSfJA4Ao5UgXu0GGhWbRKdJgSOURb_XeV9xJ1MjYbWY62\",\"childActivityUrl\":\"https%3A%2F%2Fh5static.m.jd.com%2Fmall%2Factive%2F3aEzDU3fpqYYtnNTFPAkyY3tRY8Y%2Findex.html%3Fcu%3Dtrue%26rid%3D18970%26hideyl%3D1%26utm_source%3Dlianmeng__9__kong%26utm_medium%3Djingfen%26utm_campaign%3Dt_1000565590_%26utm_term%3D92c2c87aa2494bc688a614fd6c3730e5\",\"userArea\":\"-1\",\"client\":\"-1\",\"clientVersion\":\"-1\",\"uuid\":\"-1\",\"osVersion\":\"-1\",\"brand\":\"-1\",\"model\":\"-1\",\"networkType\":\"-1\",\"jda\":\"122270672.17489355538261185537949.1748935553.1749297867.1749303607.9\",\"jsToken\":\"jdd03QNOPZQ6KTBCMMYUWXDL7AWW5IOQU66SBZFOSLZAQ4N36HWUDSYJP4CVZVJ6P}",
  'screen': "905*801",
  'client': "wh5",
  'clientVersion': "1.0.0",
  'sid': "",
  'uuid': "1749533552082130966275",
  'area': "",
  'uemps': "",
  'rfs': "",
  'xAPIClientLanguage': "zh_CN",
  'appid': "wh5",
  'ext': "{\"sdkToken\":null}",
  'functionId': "babelGetGuideTips",
  'h5st': "20250610134104619;pad3pgx9zhjj0a35;35fa0;tk03wc2e61bea18nmyeYyNl3L8tcWtr66GbqphBmdkRdxa0nEatAS0X4sX6p4R1aOQQ-t73WAn1CC1TiJotfZnM3HeW5;bb52ad81d4016fc8ea541dbbceb89c3abb479e460c310b7ab3fba5887aca40bd;5.1;1749534059619;t6HsMa7iNtLUENIQ1O3V_hrVNtHmOGLm_VImOuMsCWbiOGLmAh4WMusmk_Mm_Kri9W4W6ubg2irVNhIW6ubi_eIW6qog9mYiNpbg4OLmOGLm_VqTHlYV3lsmOGujMubV2Krh8aYg3iLi7O7i6iriKhYW7iYi5urV6ioiNlLiMuMgMiXW41YWLlsmOGuj_uMgMebRMlsmOGujMaLj92ch4xZVCJIVPZrUMuMgMWHmOuMsCmsSnhXgplLVKZImOGLmBxoVApISMusmk_Mm8iLTFRJmOGLmItHmOuMsC6nmOGOiOGLm9qbRMlsmOusmk_Mi9uMgMubi5lImOusmOGuj26sm0mMi9aHWMusmOuMsCmcT6aKUYdqdKlracJLmOGLmBxoVApISMusmOuMsCurm0msg5lImOusmOGuj_uMgMSbRMlsmOusmk_ci7uMgMWbRMlsmOusmk_siOGLm5aHWMusmOuMsCurm0msh5lImOusmOGuj4arm0m8i5lImOusmOGujMaLj92siPZoRF9ImOGLm9aHWMusmOuMsCurm0m8U3lsmOusmk_ciBuMgMinTMusmOuMsCurm0msTMusmOuMsCurm0msV3lsmOusmkCnm0msVAZoR2ZImOuMsC6nmOGOmCdIb95aRNZ5S1FaUPdIUMuMgMqrSMusmOuMsztMgMunSMusmk_Mm6WrQOCrh42YUXt8g_2si9usZgt8S3xoVAJ4ZMuMgMqYR7lsmOG_Q;438f77565db19192e5ed945d40bc2705456e412cc1a705e5a39ed6dde8599028;ri_uKJKT-JoRL1YRI9MT-J4S8ZIZ61YVF94WCeHTJJoTL9cQKxIWCeYU_tXW",
  'eid': "ZB5YRDCYCMLBLDWNBE7KXAQSP37MXN3FHPDUBDZM3BEKI3VYQMBWYHXSQRF6KXRVXYQ6A3SSBOOC4BX2YCA4T63UPE",
  'x-api-eid-token': "jdd03ZB5YRDCYCMLBLDWNBE7KXAQSP37MXN3FHPDUBDZM3BEKI3VYQMBWYHXSQRF6KXRVXYQ6A3SSBOOC4BX2YCA4T63UPEAAAAMXLBNOIFYAAAAADFN3KIXOU7M6VAX"
}

headers = {
  'User-Agent': "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36 NetType/WIFI MicroMessenger/7.0.20.1781(0x6700143B) WindowsWechat(0x63090c33) XWEB/13639 Flue",
  'x-babel-actid': "3aEzDU3fpqYYtnNTFPAkyY3tRY8Y",
  'x-referer-page': "https://pro.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html",
  'x-rp-client': "h5_1.0.0",
  'origin': "https://pro.m.jd.com",
  'sec-fetch-site': "same-site",
  'sec-fetch-mode': "cors",
  'sec-fetch-dest': "empty",
  'referer': "https://pro.m.jd.com/mall/active/3aEzDU3fpqYYtnNTFPAkyY3tRY8Y/index.html?PTAG=17053.1.1&utm_source=weixin&utm_medium=weixin&utm_campaign=t_1000072672_17053_001&preventPV=1&forceCurrentView=1",
  'accept-language': "zh-CN,zh;q=0.9",
  'priority': "u=1, i",
  'Cookie': "__jdc=122270672; mba_muid=1749533552082130966275; b_dpr=1; b_webp=1; b_avif=1; 3AB9D23F7A4B3C9B=ZB5YRDCYCMLBLDWNBE7KXAQSP37MXN3FHPDUBDZM3BEKI3VYQMBWYHXSQRF6KXRVXYQ6A3SSBOOC4BX2YCA4T63UPE; shshshfpa=a68519a2-6d67-71a5-ea5e-3910ddb07e51-1748104064; shshshfpx=a68519a2-6d67-71a5-ea5e-3910ddb07e51-1748104064; TrackerID=j1u64BbrF_fJv38YkYFTfsDWOzN77WHPviF4kFdCbA2O58kjTMAUWP81Gt_GqQ7be-WvXgVQ0xRGPW5f2M5IH043WiLYJxHrR1g5wcBmSPZ3Patxdt6PbqAF92NKbuPvVeLHtBM0mcV9U4MR1pupBA; pt_key=AAJoR8N3ADD431Qs0V4u2rJVb4mKUPpBY9Xx9Zou77ja_-sypUXu78fC4Jx5Ge0cuXe9rJHSVtA; pt_pin=jd_iJysUauMqNdl; pt_token=0yhoxb2i; pwdt_id=jd_iJysUauMqNdl; sfstoken=tk01ma0d01be2a8sMSszeDIrMnNCsE3rIJm5+LveWKX05LDYMYZsvakW87/v/DwC6J3A7xz04yuBtvbVJzTQQ1ZgpKiO; warehistory=\"10152083785333,\"; wxa_level=1; retina=0; cid=9; wqmnx1=MDEyNjM2NHNlZHB0MjV0dXBjeT1jdW5zMTRtPWVnbzZZT1IzZ0Fhc1hGYXN1dW5hbXVzdW09MSZlZnNlMzZjODAxXzZsMGRUOzQpZXQzVGljaDEwZjNOZSBNZzA3NjNuZTAwWDNsMllhLTQxUlMjISk%3D; jxsid=17495335652243256482; appCode=msd95910c4; webp=1; visitkey=5257304537198460664; __wga=1749533566095.1749533566095.1749533566095.1749533566095.1.1; PPRD_P=UUID.1749533552082130966275-LOGID.1749533566101.2132786378; jxsid_s_t=1749533566197; jxsid_s_u=https%3A//item.m.jd.com/product/10152083785333.html; sc_width=1920; __jda=122270672.1749533552082130966275.1749533552.1749533552.1749533674.2; pt_st=1_NBf-6ZV_6qRNYXP3J4j__Fz-c6gWhB8nxiZLApiDNlLIIq9drTA_UzxgO-I5Bklo4TBsxIkh6BvulZcy-syHSdfr9UDwnT-p_RaNnaii5jReKNJ2fpMtbP0KHiVEzUTrkJ-l1oSC8P44PZHpg9D0cHepyl6fp61drpKf30osovc4mJ3r7fqykucESm5QWtq66-1t8izXqV4wrNQMotbSP_BLMRSrnNfnE41q0fWep5Pg; mba_sid=17495335520841589194075.7; 3AB9D23F7A4B3CSS=jdd03ZB5YRDCYCMLBLDWNBE7KXAQSP37MXN3FHPDUBDZM3BEKI3VYQMBWYHXSQRF6KXRVXYQ6A3SSBOOC4BX2YCA4T63UPEAAAAMXLBNOIFYAAAAADFN3KIXOU7M6VAX; _gia_d=1; network=wifi; b_dw=905; b_dh=801; shshshfpb=BApXSq2VSW_JAc7eynyCfEViXUGdmds-LBgpRR0sS9xJ1PdZfQo_8iwnejT7XIqh3f6T7kann; sdtoken=AAbEsBpEIOVjqTAKCQtvQu17CKwWToZf9sRHOVEmX-VHuyTrtFVdjqGuw_3XFF5PmasebeY-xTUI3s1udPEp1d802CzRqN4ZvPFHr_xpPAw5AsdBQhH6pmyx6R3SAoAttRokfY2RkQ; __jd_ref_cls=Babel_H5FirstClick; __jdb=122270672.5.1749533552082130966275|2.1749533674; __jdv=122270672%7Cweixin%7Ct_1000072672_17053_001%7Cweixin%7CWxfriends_shareidcf436283365017ac174918144280792161_none_none%7C1749534059451; joyya=1749534059.0.42.06wreyx; joyytokem="
}

async def send_request(session: aiohttp.ClientSession):
    """异步发送请求（禁用证书验证）"""
    try:
        async with session.post(url, data=payload, headers=headers) as resp:
            return await resp.text()
    except aiohttp.ClientError as e:
        print(f"请求错误: {e}")
        return None


async def get_jd_time() -> float:
    """获取京东服务器时间（异步请求）"""
    url = "https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5"
    try:
        async with aiohttp.ClientSession() as session:
            async with session.head(url, timeout=3) as response:
                # 从响应头中提取服务器时间（UTC+8）
                server_time_str = response.headers.get('Date', '')
                server_time = datetime.datetime.strptime(
                    server_time_str, '%a, %d %b %Y %H:%M:%S GMT'
                ).timestamp() + 28800  # GMT+8时区校准
                local_time = time.time()
                print(f"京东服务器时间: {datetime.datetime.fromtimestamp(server_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                print(f"本地系统时间: {datetime.datetime.fromtimestamp(local_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
                print(f"时间差异: {local_time - server_time:.3f}秒")
    ##发现获取网络时间可能不准，直接用本地算了
                return local_time
    except Exception as e:
        print(f"获取京东时间失败: {e}")
        local_time = time.time()
        print(f"使用本地时间: {datetime.datetime.fromtimestamp(local_time).strftime('%Y-%m-%d %H:%M:%S.%f')[:-3]}")
        return local_time  # 降级为本地时间


async def wait_until(target_jd_time: float):
    """等待至京东服务器时间到达目标时刻"""
    while True:
        current_jd_time = await get_jd_time()  # 实时获取京东时间
        remaining = target_jd_time - current_jd_time
        if remaining <= 0:
            break
        # 动态调整等待间隔（平衡请求频率和精度）
        if remaining > 1.0:
            await asyncio.sleep(min(remaining / 2, 1.0))
        elif remaining > 0.1:
            await asyncio.sleep(0.05)
        else:
            await asyncio.sleep(0.001)





async def main():
    # 定义多个抢购目标时间（京东时区 GMT+8）
    time_points = [
        (7, 59, 59, 899000),
        (14, 59, 59, 899000),
        (17, 59, 59, 899000),
        (19, 59, 59, 899000),
        (21, 59, 59, 899000),
    ]
    now = await get_jd_time()
    today = datetime.datetime.fromtimestamp(now)
    for hour, minute, second, microsecond in time_points:
        # 生成今天的目标时间戳
        target_time = today.replace(hour=hour, minute=minute, second=second, microsecond=microsecond).timestamp()
        # 若已过该时间点则跳过
        if now >= target_time:
            continue
        human_time = datetime.datetime.fromtimestamp(target_time).strftime(
            "%Y年%m月%d日 %H时%M分%S.%f"
        )[:-3] + "秒"
        print(f"[京东同步] 等待至抢购开始时间: {human_time}")
        await wait_until(target_time)
        print(f"[系统] {hour:02d}:{minute:02d} 时间到达，执行抢购！")
        # 创建会话，禁用证书验证
        async with aiohttp.ClientSession(connector=aiohttp.TCPConnector(ssl=ssl_context)) as session:
            tasks = [send_request(session) for _ in range(50)]
            responses = await asyncio.gather(*tasks)
            for i, res in enumerate(responses):
                print(f"Request {i+1}: {res}")
                if '领取成功' in res:
                    send('京东100-50领取成功')
        # 抢完后更新时间，防止后续时间点被跳过
        now = await get_jd_time()
    print("[系统] 今日所有抢购时间段已结束")
    


if __name__ == "__main__":
    
    asyncio.run(main())