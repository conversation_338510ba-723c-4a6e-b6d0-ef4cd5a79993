import time
import datetime
import aiohttp

def get_jd_timestamp():
    """获取京东服务器时间戳（同步版本）"""
    url = "https://api.m.jd.com/client.action?functionId=queryMaterialProducts&client=wh5"
    try:
        import requests
        response = requests.head(url, timeout=3)
        # 从响应头中提取服务器时间（UTC+8）
        server_time_str = response.headers.get('Date', '')
        server_time = datetime.datetime.strptime(
            server_time_str, '%a, %d %b %Y %H:%M:%S GMT'
        ).timestamp() + 28800  # GMT+8时区校准
        return server_time
    except Exception as e:
        print(f"获取京东时间失败: {e}")
        return time.time()  # 降级为本地时间

async def get_jd_timestamp_async():
    """获取京东服务器时间戳（异步版本）"""
    return await get_jd_time()  # 复用已有的异步函数